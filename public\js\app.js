// 全局变量
let currentUser = null;
let authToken = sessionStorage.getItem('authToken');

// API基础URL
const API_BASE = '/api';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 清除localStorage中的旧token（如果存在）
    localStorage.removeItem('authToken');

    if (authToken) {
        checkAuth();
    } else {
        showLoginModal();
    }
});

// 检查认证状态
async function checkAuth() {
    try {
        const response = await fetch(`${API_BASE}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            currentUser = data.data.user;
            document.getElementById('username').textContent = currentUser.username;
            hideLoginModal();
            loadDashboard();
        } else {
            // 认证失败，清除所有存储并显示登录界面
            sessionStorage.removeItem('authToken');
            localStorage.removeItem('authToken');
            authToken = null;
            showLoginModal();

            if (response.status === 401) {
                showNotification('登录已过期，请重新登录', 'error');
            }
        }
    } catch (error) {
        console.error('认证检查失败:', error);
        showLoginModal();
    }
}

// 显示登录模态框
function showLoginModal() {
    document.getElementById('login-modal').classList.remove('hidden');
}

// 隐藏登录模态框
function hideLoginModal() {
    document.getElementById('login-modal').classList.add('hidden');
}

// 登录表单提交
document.getElementById('login-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const username = document.getElementById('login-username').value;
    const password = document.getElementById('login-password').value;
    const errorDiv = document.getElementById('login-error');
    
    try {
        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            authToken = data.data.token;
            sessionStorage.setItem('authToken', authToken);
            currentUser = data.data.user;
            document.getElementById('username').textContent = currentUser.username;
            hideLoginModal();
            loadDashboard();
            errorDiv.classList.add('hidden');

            // 显示登录成功提示
            showNotification(`欢迎回来，${currentUser.username}！`, 'success');

            // 提醒用户关于会话管理
            setTimeout(() => {
                showNotification('提示：关闭浏览器将自动退出登录', 'info');
            }, 2000);
        } else {
            errorDiv.textContent = data.message || '登录失败';
            errorDiv.classList.remove('hidden');
        }
    } catch (error) {
        console.error('登录错误:', error);
        errorDiv.textContent = '网络错误，请稍后重试';
        errorDiv.classList.remove('hidden');
    }
});

// 退出登录
function logout() {
    // 清除所有存储的认证信息
    sessionStorage.removeItem('authToken');
    localStorage.removeItem('authToken'); // 确保清除旧的localStorage数据
    authToken = null;
    currentUser = null;

    // 显示登录模态框
    showLoginModal();

    // 显示退出成功提示
    showNotification('已安全退出登录', 'success');
}

// 切换侧边栏
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    
    if (sidebar.style.transform === 'translateX(-100%)') {
        sidebar.style.transform = 'translateX(0)';
        mainContent.style.marginLeft = '16rem';
    } else {
        sidebar.style.transform = 'translateX(-100%)';
        mainContent.style.marginLeft = '0';
    }
}

// 切换用户菜单
function toggleUserMenu() {
    const userMenu = document.getElementById('user-menu');
    userMenu.classList.toggle('hidden');
}

// 点击外部关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.getElementById('user-menu');
    if (!e.target.closest('.relative')) {
        userMenu.classList.add('hidden');
    }
});

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };

    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(`${API_BASE}${url}`, mergedOptions);
        const data = await response.json();

        if (!response.ok) {
            if (response.status === 401) {
                logout();
                return;
            }
            throw new Error(data.message || '请求失败');
        }

        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 显示通知
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');

    let bgColor = 'bg-green-500';
    let icon = '✓';

    switch(type) {
        case 'success':
            bgColor = 'bg-green-500';
            icon = '✓';
            break;
        case 'error':
            bgColor = 'bg-red-500';
            icon = '✗';
            break;
        case 'info':
            bgColor = 'bg-blue-500';
            icon = 'ℹ';
            break;
        case 'warning':
            bgColor = 'bg-yellow-500';
            icon = '⚠';
            break;
    }

    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${bgColor} text-white flex items-center gap-2`;
    notification.innerHTML = `
        <span class="font-bold">${icon}</span>
        <span>${message}</span>
    `;

    document.body.appendChild(notification);

    // 添加进入动画
    notification.style.transform = 'translateX(100%)';
    notification.style.transition = 'transform 0.3s ease';
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    // 自动移除
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 设置活动导航项
function setActiveNavItem(activeItem) {
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('bg-blue-50', 'text-blue-600');
        item.classList.add('text-gray-700');
    });
    
    if (activeItem) {
        activeItem.classList.add('bg-blue-50', 'text-blue-600');
        activeItem.classList.remove('text-gray-700');
    }
}

// 加载仪表盘
async function loadDashboard() {
    try {
        const stats = await apiRequest('/orders/stats/overview');
        
        document.getElementById('today-orders').textContent = stats.data.today.orders;
        document.getElementById('today-amount').textContent = `¥${stats.data.today.amount.toFixed(2)}`;
        
        // 加载其他统计数据
        loadDashboardStats();
    } catch (error) {
        console.error('加载仪表盘失败:', error);
    }
}

// 加载仪表盘统计
async function loadDashboardStats() {
    try {
        // 获取商品总数
        const products = await apiRequest('/products?limit=1');
        document.getElementById('total-products').textContent = products.data.pagination.total;
        
        // 获取用户总数
        const users = await apiRequest('/users?limit=1');
        document.getElementById('total-users').textContent = users.data.pagination.total;
        
        // 获取最近订单
        const orders = await apiRequest('/orders?limit=5');
        displayRecentOrders(orders.data.orders);
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 显示最近订单
function displayRecentOrders(orders) {
    const container = document.getElementById('recent-orders');
    
    if (orders.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-8">暂无订单数据</p>';
        return;
    }
    
    container.innerHTML = orders.map(order => `
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
                <p class="font-medium text-gray-900">${order.order_number}</p>
                <p class="text-sm text-gray-500">${order.customer_name} - ${order.customer_phone}</p>
            </div>
            <div class="text-right">
                <p class="font-medium text-gray-900">¥${parseFloat(order.total_amount).toFixed(2)}</p>
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}">
                    ${getStatusText(order.status)}
                </span>
            </div>
        </div>
    `).join('');
}

// 获取状态颜色
function getStatusColor(status) {
    const colors = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-blue-100 text-blue-800',
        'processing': 'bg-purple-100 text-purple-800',
        'shipped': 'bg-indigo-100 text-indigo-800',
        'delivered': 'bg-green-100 text-green-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
}

// 获取状态文本
function getStatusText(status) {
    const texts = {
        'pending': '待处理',
        'confirmed': '已确认',
        'processing': '处理中',
        'shipped': '已发货',
        'delivered': '已送达',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

// 显示仪表盘
function showDashboard() {
    document.getElementById('page-title').textContent = '仪表盘';
    setActiveNavItem(event.target.closest('.nav-item'));
    
    document.getElementById('content-area').innerHTML = `
        <div id="dashboard-content" class="fade-in">
            ${document.getElementById('dashboard-content').innerHTML}
        </div>
    `;
    
    loadDashboard();
}

// 显示订单管理
function showOrders() {
    document.getElementById('page-title').textContent = '订单管理';
    setActiveNavItem(event.target.closest('.nav-item'));
    
    document.getElementById('content-area').innerHTML = `
        <div class="fade-in">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">订单列表</h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-500 text-center py-8">订单管理功能开发中...</p>
                </div>
            </div>
        </div>
    `;
}

// 显示商品管理
function showProducts() {
    document.getElementById('page-title').textContent = '商品管理';
    setActiveNavItem(event.target.closest('.nav-item'));
    
    document.getElementById('content-area').innerHTML = `
        <div class="fade-in">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">商品列表</h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-500 text-center py-8">商品管理功能开发中...</p>
                </div>
            </div>
        </div>
    `;
}

// 显示属性管理
function showAttributes() {
    document.getElementById('page-title').textContent = '属性管理';
    setActiveNavItem(event.target.closest('.nav-item'));
    
    document.getElementById('content-area').innerHTML = `
        <div class="fade-in">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">属性管理</h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-500 text-center py-8">属性管理功能开发中...</p>
                </div>
            </div>
        </div>
    `;
}

// 显示用户管理
function showUsers() {
    document.getElementById('page-title').textContent = '用户管理';
    setActiveNavItem(event.target.closest('.nav-item'));
    
    document.getElementById('content-area').innerHTML = `
        <div class="fade-in">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">用户列表</h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-500 text-center py-8">用户管理功能开发中...</p>
                </div>
            </div>
        </div>
    `;
}

// 修改密码
function changePassword() {
    // TODO: 实现修改密码功能
    showNotification('修改密码功能开发中', 'info');
}

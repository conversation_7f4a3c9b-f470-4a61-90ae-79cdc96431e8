const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const database = require('./config/database');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const attributeRoutes = require('./routes/attributes');
const orderRoutes = require('./routes/orders');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());
app.use(cors({
    origin: process.env.NODE_ENV === 'production' ? ['https://yourdomain.com'] : true,
    credentials: true
}));

// 限流中间件
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/admin', express.static(path.join(__dirname, 'public')));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/products', productRoutes);
app.use('/api/attributes', attributeRoutes);
app.use('/api/orders', orderRoutes);

// 前台首页
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'world-class-printing-ui.html'));
});

// 登录页面
app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

// 管理后台首页 - 需要认证
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 管理后台静态资源 - 重定向未认证用户到登录页面
app.use('/admin', (req, res, next) => {
    // 对于静态资源请求，直接通过
    if (req.path.includes('.') || req.path.includes('js') || req.path.includes('css')) {
        return next();
    }

    // 对于页面请求，检查是否有认证token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token && !req.headers.referer?.includes('/admin')) {
        // 没有token且不是从admin页面来的请求，重定向到登录页面
        return res.redirect('/login');
    }

    next();
});

// 会话管理测试页面
app.get('/test-session.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'test-session.html'));
});

// 前台API - 获取商品列表
app.get('/api/public/products', async (req, res) => {
    try {
        const products = await database.query(
            'SELECT id, name, description, category, base_price, images FROM products WHERE status = "active" ORDER BY created_at DESC'
        );

        const productsWithImages = products.map(product => ({
            ...product,
            images: product.images ? JSON.parse(product.images) : []
        }));

        res.json({
            success: true,
            data: { products: productsWithImages }
        });
    } catch (error) {
        console.error('获取商品列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取商品列表失败'
        });
    }
});

// 前台API - 提交咨询
app.post('/api/public/inquiry', async (req, res) => {
    try {
        const { name, phone, email, company, message } = req.body;

        if (!name || !phone || !message) {
            return res.status(400).json({
                success: false,
                message: '姓名、电话和咨询内容不能为空'
            });
        }

        // 这里可以保存到数据库或发送邮件
        console.log('收到咨询:', { name, phone, email, company, message });

        res.json({
            success: true,
            message: '咨询提交成功，我们会尽快联系您！'
        });
    } catch (error) {
        console.error('提交咨询错误:', error);
        res.status(500).json({
            success: false,
            message: '提交失败，请稍后重试'
        });
    }
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({ 
        success: false, 
        message: '接口不存在' 
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({ 
        success: false, 
        message: process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message 
    });
});

// 启动服务器
async function startServer() {
    try {
        // 连接数据库
        await database.connect();

        app.listen(PORT, () => {
            console.log(`🚀 友文印刷后台管理系统启动成功`);
            console.log(`📱 服务地址: http://localhost:${PORT}`);
            console.log(`🔧 管理后台: http://localhost:${PORT}/admin`);
            console.log(`🌍 环境: ${process.env.NODE_ENV}`);
        });
    } catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}

startServer();

module.exports = app;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔐 友文印刷会话管理测试</h1>
    
    <div class="test-card">
        <h3>当前存储状态</h3>
        <div id="storage-status"></div>
        <button onclick="checkStorage()">检查存储</button>
        <button onclick="clearAllStorage()">清除所有存储</button>
    </div>
    
    <div class="test-card">
        <h3>登录测试</h3>
        <div>
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="admin123456">
            <button onclick="testLogin()">测试登录</button>
        </div>
        <div id="login-result"></div>
    </div>
    
    <div class="test-card">
        <h3>API测试</h3>
        <button onclick="testAPI()">测试API调用</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-card">
        <h3>会话管理说明</h3>
        <div class="info">
            <h4>✅ 修改后的行为：</h4>
            <ul>
                <li><strong>使用sessionStorage</strong>：关闭浏览器标签页自动退出</li>
                <li><strong>清除localStorage</strong>：移除旧的持久化token</li>
                <li><strong>自动检测过期</strong>：token过期自动退出并提示</li>
                <li><strong>安全提示</strong>：登录界面显示会话管理说明</li>
            </ul>
            
            <h4>🔒 安全特性：</h4>
            <ul>
                <li>关闭浏览器 = 自动退出登录</li>
                <li>token过期 = 自动跳转登录页</li>
                <li>手动退出 = 清除所有认证信息</li>
                <li>页面刷新 = 保持登录状态（同一会话内）</li>
            </ul>
        </div>
    </div>
    
    <div class="test-card">
        <h3>快速访问</h3>
        <button onclick="window.open('/admin', '_blank')">打开管理后台</button>
        <button onclick="window.open('/', '_blank')">打开前台首页</button>
    </div>

    <script>
        function checkStorage() {
            const sessionToken = sessionStorage.getItem('authToken');
            const localToken = localStorage.getItem('authToken');
            
            let html = '<div class="status info">';
            html += '<strong>存储检查结果：</strong><br>';
            html += `sessionStorage.authToken: ${sessionToken ? '✅ 存在' : '❌ 不存在'}<br>`;
            html += `localStorage.authToken: ${localToken ? '⚠️ 存在（应该被清除）' : '✅ 不存在'}`;
            html += '</div>';
            
            document.getElementById('storage-status').innerHTML = html;
        }
        
        function clearAllStorage() {
            sessionStorage.clear();
            localStorage.clear();
            document.getElementById('storage-status').innerHTML = '<div class="status success">所有存储已清除</div>';
        }
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    sessionStorage.setItem('authToken', data.data.token);
                    document.getElementById('login-result').innerHTML = 
                        '<div class="status success">登录成功！Token已存储到sessionStorage</div>';
                } else {
                    document.getElementById('login-result').innerHTML = 
                        `<div class="status error">登录失败：${data.message}</div>`;
                }
            } catch (error) {
                document.getElementById('login-result').innerHTML = 
                    `<div class="status error">网络错误：${error.message}</div>`;
            }
        }
        
        async function testAPI() {
            const token = sessionStorage.getItem('authToken');
            
            if (!token) {
                document.getElementById('api-result').innerHTML = 
                    '<div class="status error">没有找到token，请先登录</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('api-result').innerHTML = 
                        `<div class="status success">API调用成功！用户：${data.data.user.username}</div>`;
                } else {
                    document.getElementById('api-result').innerHTML = 
                        `<div class="status error">API调用失败：${data.message}</div>`;
                }
            } catch (error) {
                document.getElementById('api-result').innerHTML = 
                    `<div class="status error">网络错误：${error.message}</div>`;
            }
        }
        
        // 页面加载时检查存储状态
        window.onload = function() {
            checkStorage();
        };
    </script>
</body>
</html>

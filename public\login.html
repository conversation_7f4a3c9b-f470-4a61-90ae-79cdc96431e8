<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 友文印刷管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .input-group {
            position: relative;
        }
        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label {
            transform: translateY(-1.5rem) scale(0.875);
            color: #4f46e5;
        }
        .floating-label {
            position: absolute;
            left: 0.75rem;
            top: 0.75rem;
            transition: all 0.2s ease-in-out;
            pointer-events: none;
            color: #6b7280;
        }
        .login-input {
            transition: all 0.2s ease-in-out;
        }
        .login-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.2);
        }
        .login-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px -5px rgba(79, 70, 229, 0.4);
        }
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        .slide-up {
            animation: slideUp 0.8s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        .logo-animation {
            animation: logoFloat 3s ease-in-out infinite;
        }
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="login-bg">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo和标题 -->
            <div class="text-center fade-in">
                <div class="logo-animation inline-block">
                    <div class="w-20 h-20 mx-auto bg-white rounded-full flex items-center justify-center shadow-lg">
                        <i class="fas fa-print text-3xl text-indigo-600"></i>
                    </div>
                </div>
                <h2 class="mt-6 text-3xl font-extrabold text-white">
                    友文印刷管理系统
                </h2>
                <p class="mt-2 text-sm text-indigo-100">
                    请登录您的管理员账户
                </p>
            </div>

            <!-- 登录表单 -->
            <div class="login-card rounded-2xl p-8 slide-up">
                <form id="login-form" class="space-y-6">
                    <!-- 用户名输入 -->
                    <div class="input-group">
                        <input 
                            id="username" 
                            name="username" 
                            type="text" 
                            required 
                            placeholder=" "
                            class="login-input appearance-none rounded-lg relative block w-full px-3 py-3 border border-gray-300 text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:z-10"
                        >
                        <label for="username" class="floating-label">
                            <i class="fas fa-user mr-2"></i>用户名
                        </label>
                    </div>

                    <!-- 密码输入 -->
                    <div class="input-group">
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            placeholder=" "
                            class="login-input appearance-none rounded-lg relative block w-full px-3 py-3 border border-gray-300 text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:z-10"
                        >
                        <label for="password" class="floating-label">
                            <i class="fas fa-lock mr-2"></i>密码
                        </label>
                    </div>

                    <!-- 记住我选项 -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input 
                                id="remember-me" 
                                name="remember-me" 
                                type="checkbox" 
                                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                            >
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                                记住我
                            </label>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div id="error-message" class="hidden">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700" id="error-text"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <div>
                        <button 
                            type="submit" 
                            id="login-btn"
                            class="login-btn group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-sign-in-alt text-indigo-300 group-hover:text-indigo-200"></i>
                            </span>
                            <span id="login-text">登录</span>
                            <span id="login-loading" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-2"></i>登录中...
                            </span>
                        </button>
                    </div>

                    <!-- 安全提示 -->
                    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-shield-alt text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700">
                                    <strong>安全提示：</strong>关闭浏览器将自动退出登录，保护您的账户安全。
                                </p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 版权信息 -->
            <div class="text-center fade-in">
                <p class="text-indigo-100 text-sm">
                    © 2024 友文印刷. 保留所有权利.
                </p>
            </div>
        </div>
    </div>

    <script src="js/login.js"></script>
</body>
</html>

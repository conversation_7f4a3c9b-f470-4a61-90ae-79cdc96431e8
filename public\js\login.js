// 登录页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查URL参数中的消息
    const urlParams = new URLSearchParams(window.location.search);
    const message = urlParams.get('message');
    if (message) {
        showError(message);
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // 检查是否已经登录
    const authToken = sessionStorage.getItem('authToken');
    if (authToken) {
        // 验证token是否有效
        checkAuthAndRedirect();
    }

    // 绑定登录表单事件
    const loginForm = document.getElementById('login-form');
    loginForm.addEventListener('submit', handleLogin);

    // 添加输入框动画效果
    const inputs = document.querySelectorAll('.login-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });

    // 键盘事件处理
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            const loginBtn = document.getElementById('login-btn');
            if (!loginBtn.disabled) {
                loginForm.dispatchEvent(new Event('submit'));
            }
        }
    });
});

// 检查认证状态并重定向
async function checkAuthAndRedirect() {
    try {
        const authToken = sessionStorage.getItem('authToken');
        const response = await fetch('/api/auth/me', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            // 已登录，重定向到管理页面
            window.location.href = '/admin';
        } else {
            // token无效，清除并继续显示登录页面
            sessionStorage.removeItem('authToken');
        }
    } catch (error) {
        console.error('认证检查失败:', error);
        sessionStorage.removeItem('authToken');
    }
}

// 处理登录表单提交
async function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    // 验证输入
    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }

    // 显示加载状态
    setLoadingState(true);
    hideError();

    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                username, 
                password,
                rememberMe 
            })
        });

        const data = await response.json();

        if (response.ok) {
            // 登录成功
            const token = data.data.token;
            const user = data.data.user;

            // 存储认证信息
            sessionStorage.setItem('authToken', token);
            
            // 如果选择了记住我，可以在这里添加额外的持久化逻辑
            if (rememberMe) {
                // 可以设置一个标记，表示用户希望被记住
                localStorage.setItem('rememberUser', 'true');
            }

            // 显示成功消息
            showSuccess(`欢迎回来，${user.username}！正在跳转...`);

            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);

        } else {
            // 登录失败
            showError(data.message || '登录失败，请检查用户名和密码');
            
            // 清空密码字段
            document.getElementById('password').value = '';
            document.getElementById('password').focus();
        }
    } catch (error) {
        console.error('登录错误:', error);
        showError('网络错误，请检查网络连接后重试');
    } finally {
        setLoadingState(false);
    }
}

// 设置加载状态
function setLoadingState(loading) {
    const loginBtn = document.getElementById('login-btn');
    const loginText = document.getElementById('login-text');
    const loginLoading = document.getElementById('login-loading');
    const inputs = document.querySelectorAll('.login-input');

    if (loading) {
        loginBtn.disabled = true;
        loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
        loginText.classList.add('hidden');
        loginLoading.classList.remove('hidden');
        
        // 禁用输入框
        inputs.forEach(input => input.disabled = true);
    } else {
        loginBtn.disabled = false;
        loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
        loginText.classList.remove('hidden');
        loginLoading.classList.add('hidden');
        
        // 启用输入框
        inputs.forEach(input => input.disabled = false);
    }
}

// 显示错误消息
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    
    errorText.textContent = message;
    errorDiv.classList.remove('hidden');
    
    // 添加震动效果
    errorDiv.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        errorDiv.style.animation = '';
    }, 500);
}

// 隐藏错误消息
function hideError() {
    const errorDiv = document.getElementById('error-message');
    errorDiv.classList.add('hidden');
}

// 显示成功消息
function showSuccess(message) {
    // 创建成功提示元素
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    successDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(successDiv);
    
    // 显示动画
    setTimeout(() => {
        successDiv.classList.remove('translate-x-full');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        successDiv.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(successDiv);
        }, 300);
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
